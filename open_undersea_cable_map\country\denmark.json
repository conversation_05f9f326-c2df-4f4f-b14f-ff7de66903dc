{"id": "denmark", "name": "Denmark", "cables": [{"id": "baltica", "name": "Baltica", "rfs_year": 1997, "is_planned": false}, {"id": "cantat-3", "name": "CANTAT-3", "rfs_year": 1994, "is_planned": false}, {"id": "cobracable", "name": "COBRAcable", "rfs_year": 2019, "is_planned": false}, {"id": "danica-north", "name": "Danica North", "rfs_year": 1998, "is_planned": false}, {"id": "danice", "name": "DANICE", "rfs_year": 2009, "is_planned": false}, {"id": "denmark-poland-2", "name": "Denmark-Poland 2", "rfs_year": 1991, "is_planned": false}, {"id": "denmark-sweden-15", "name": "Denmark-Sweden 15", "rfs_year": 1989, "is_planned": false}, {"id": "denmark-sweden-16", "name": "Denmark-Sweden 16", "rfs_year": 1991, "is_planned": false}, {"id": "denmark-sweden-17", "name": "Denmark-Sweden 17", "rfs_year": 1994, "is_planned": false}, {"id": "denmark-sweden-18", "name": "Denmark-Sweden 18", "rfs_year": 1996, "is_planned": false}, {"id": "elektra-globalconnect-1-gc1", "name": "Elektra-GlobalConnect 1 (GC1)", "rfs_year": 2000, "is_planned": false}, {"id": "energinet-laeso-varberg", "name": "<PERSON><PERSON><PERSON><PERSON>", "rfs_year": 2011, "is_planned": false}, {"id": "energinet-lyngsa-laeso", "name": "Energinet Lyngsa-Laeso", "rfs_year": 2011, "is_planned": false}, {"id": "fehmarn-blt", "name": "<PERSON><PERSON><PERSON><PERSON>", "rfs_year": 2000, "is_planned": false}, {"id": "germany-denmark-3", "name": "Germany-Denmark 3", "rfs_year": 2000, "is_planned": false}, {"id": "globalconnect-2-gc2", "name": "GlobalConnect 2 (GC2)", "rfs_year": 2001, "is_planned": false}, {"id": "globalconnect-3-gc3", "name": "GlobalConnect 3 (GC3)", "rfs_year": 2006, "is_planned": false}, {"id": "globalconnect-kpn", "name": "GlobalConnect-KPN", "rfs_year": 2006, "is_planned": false}, {"id": "havfrueaec-2", "name": "Havfrue/AEC-2", "rfs_year": 2020, "is_planned": false}, {"id": "havhingstennorth-sea-connect-nsc", "name": "Havhingsten/North Sea Connect (NSC)", "rfs_year": 2022, "is_planned": false}, {"id": "havsil", "name": "Havsi<PERSON>", "rfs_year": 2022, "is_planned": false}, {"id": "hronn", "name": "Hronn", "rfs_year": 2022, "is_planned": false}, {"id": "ip-only-denmark-sweden", "name": "IP-Only Denmark-Sweden", "rfs_year": 1994, "is_planned": false}, {"id": "kattegat-2", "name": "Kattegat 2", "rfs_year": 2001, "is_planned": false}, {"id": "rnne-rdvig", "name": "<PERSON><PERSON><PERSON>-R<PERSON>d<PERSON>g", "rfs_year": 1989, "is_planned": false}, {"id": "scandinavian-ring-north", "name": "Scandinavian Ring North", "rfs_year": 2000, "is_planned": false}, {"id": "scandinavian-ring-south", "name": "Scandinavian Ring South", "rfs_year": 2000, "is_planned": false}, {"id": "skagenfiber-west", "name": "Skagenfiber West", "rfs_year": 2020, "is_planned": false}, {"id": "skagerrak-4", "name": "Skagerrak 4", "rfs_year": 2014, "is_planned": false}], "landing_points": ["kolobrzeg-poland", "ystad-sweden", "gedser-denmark", "pedersker-denmark", "helsingborg-sweden", "gedebak-odde-denmark", "mielno-poland", "puttgarden-germany", "rodbyhavn-denmark", "helsingr-denmark", "dragor-denmark", "bunkeflostand-sweden", "tuborg-denmark", "barsebck-sweden", "blaabjerg-denmark", "lyngsa-denmark", "vestero-denmark", "osterby-denmark", "skalvik-sweden", "kristinelund-sweden", "alsgarde-denmark", "vestmannaeyjar-iceland", "tjornuvik-faroe-islands", "sylt-germany", "rostock-germany", "saeby-denmark", "kungsbacka-sweden", "korsor-denmark", "nybor-denmark", "mosede-denmark", "velling-sweden", "markgrafenheide-germany", "brondby-denmark", "klagshamn-sweden", "south-arne-denmark", "valdemar-denmark", "tyra-denmark", "kristiansand-norway", "tjele-denmark", "eemshaven-netherlands", "endrup-denmark", "newcastle-united-kingdom", "wall-township-nj-united-states", "lecanvey-ireland", "rdvig-denmark", "rnne-denmark", "larvik-norway", "hirtshals-denmark", "laeso-denmark", "varberg-sweden", "houstrup-denmark", "hanstholm-denmark", "landeyjar-iceland", "fano-denmark", "totalenergies-halfdan-denmark", "totalenergies-tyra-denmark"], "landing_points_in_country": ["rodbyhavn-denmark", "blaabjerg-denmark", "gedser-denmark", "pedersker-denmark", "gedebak-odde-denmark", "helsingr-denmark", "dragor-denmark", "tuborg-denmark", "fano-denmark", "lyngsa-denmark", "vestero-denmark", "osterby-denmark", "saeby-denmark", "alsgarde-denmark", "south-arne-denmark", "valdemar-denmark", "tyra-denmark", "brondby-denmark", "korsor-denmark", "nybor-denmark", "mosede-denmark", "tjele-denmark", "endrup-denmark", "rdvig-denmark", "rnne-denmark", "hirtshals-denmark", "laeso-denmark", "hanstholm-denmark", "houstrup-denmark", "totalenergies-halfdan-denmark", "totalenergies-tyra-denmark"]}