{"id": "greece", "name": "Greece", "cables": [{"id": "2africa", "name": "2Africa", "rfs_year": 2023, "is_planned": true}, {"id": "adria-1", "name": "Adria-1", "rfs_year": 1996, "is_planned": false}, {"id": "apollo-east-and-west", "name": "Apollo East and West", "rfs_year": 2024, "is_planned": true}, {"id": "asia-africa-europe-1-aae-1", "name": "Asia Africa Europe-1 (AAE-1)", "rfs_year": 2017, "is_planned": false}, {"id": "blue", "name": "Blue", "rfs_year": 2024, "is_planned": true}, {"id": "india-europe-xpress-iex", "name": "India Europe Xpress (IEX)", "rfs_year": 2024, "is_planned": true}, {"id": "ionian", "name": "<PERSON><PERSON>", "rfs_year": 2022, "is_planned": true}, {"id": "italy-greece-1", "name": "Italy-Greece 1", "rfs_year": 1995, "is_planned": false}, {"id": "mednautilus-submarine-system", "name": "MedNautilus Submarine System", "rfs_year": 2001, "is_planned": false}, {"id": "medusa-submarine-cable-system", "name": "Medusa Submarine Cable System", "rfs_year": 2024, "is_planned": true}, {"id": "minoas-east-and-west", "name": "Minoas East and West", "rfs_year": 2021, "is_planned": false}, {"id": "oteglobe-kokkini-bari", "name": "OTEGLOBE Ko<PERSON>kini-Bari", "rfs_year": 2004, "is_planned": false}, {"id": "seamewe-3", "name": "SeaMeWe-3", "rfs_year": 1999, "is_planned": false}, {"id": "silphium", "name": "Silphium", "rfs_year": 2013, "is_planned": false}, {"id": "thetis", "name": "<PERSON><PERSON>", "rfs_year": 2022, "is_planned": false}, {"id": "vodafone-greece-domestic", "name": "Vodafone Greece Domestic", "rfs_year": 2008, "is_planned": false}], "landing_points": ["deep-water-bay-china", "taipa-china", "batangas-philippines", "danang-vietnam", "tungku-brunei", "mersing-malaysia", "tuas-singapore", "okinawa-japan", "shanghai-china", "toucheng-taiwan", "fangshan-taiwan", "ancol-indonesia", "perth-wa-australia", "penang-malaysia", "satun-thailand", "pyapon-myanmar", "mt-lavinia-sri-lanka", "cochin-india", "mumbai-india", "karachi-pakistan", "muscat-oman", "fujairah-united-arab-emirates", "djibouti-city-djibouti", "suez-egypt", "alexandria-egypt", "yeroskipos-cyprus", "marmaris-turkey", "mazara-del-vallo-italy", "sesimbra-portugal", "penmarch-france", "goonhilly-downs-united-kingdom", "medan-indonesia", "jeddah-saudi-arabia", "catania-italy", "tel-aviv-israel", "haifa-israel", "istanbul-turkey", "chania-greece", "ttouan-morocco", "ostend-belgium", "athens-greece", "otranto-italy", "corfu-greece", "durres-albania", "dubrovnik-croatia", "kokkini-greece", "bari-italy", "pentaskhinos-cyprus", "derna-libya", "marseille-france", "abu-talat-egypt", "zafarana-egypt", "doha-qatar", "songkhla-thailand", "sihanoukville-cambodia", "aden-yemen", "al-bustan-oman", "cape-daguilar-china", "vung-tau-vietnam", "ngwe-saung-myanmar", "geoje-south-korea", "aethos-greece", "ras-ghareb-egypt", "port-said-egypt", "bude-united-kingdom", "genoa-italy", "barcelona-spain", "port-sudan-sudan", "salal<PERSON>-<PERSON>man", "mogadishu-somalia", "mombasa-kenya", "dar-es-salaam-tanzania", "maputo-mozambique", "nacala-mozambique", "mahajanga-madagascar", "mtunzini-south-africa", "cape-town-south-africa", "port-elizabeth-south-africa", "muanda-congo-dem-rep-", "pointe-noire-congo-rep-", "libreville-gabon", "lagos-nigeria", "accra-ghana", "abidjan-cte-divoire", "dakar-senegal", "yanbu-saudi-arabia", "carcavelos-portugal", "sidi-kerir-egypt", "savona-italy", "crotone-italy", "preveza-greece", "shantou-china", "palermo-italy", "rome-italy", "kwa-ibo-nigeria", "luanda-angola", "moroni-comoros", "gran-canaria-canary-islands-spain", "carana-seychelles", "butterworth-malaysia", "aqaba-jordan", "duba-saudi-arabia", "timpaki-greece", "barka-oman", "abu-dhabi-united-arab-emirates", "kalba-united-arab-emirates", "manama-bahrain", "al-faw-iraq", "kuwait-city-kuwait", "al-khobar-saudi-arabia", "bastia-france", "golfo-aranci-italy", "zahara-de-los-atunes-spain", "torreguadiaro-spain", "nador-morocco", "algiers-algeria", "collo-algeria", "tympaki-greece", "korakia-greece", "pachi-greece", "sines-portugal", "nopigeia-greece", "neapoli-greece", "berbera-somalia", "porto-rafti-greece", "syros-greece", "sitia-greece", "plimmiri-greece", "kremasti-greece", "kardamena-greece", "kochilari-greece", "perivolos-greece", "baxedes-greece", "pirgaki-greece", "naxos-greece", "naousa-greece", "kala<PERSON><PERSON>-greece", "mykonos-greece", "agios-sostis-greece", "tinos-greece", "ermoupoli-greece", "kavos-greece", "kontokali-greece", "plataria-greece", "filizi-greece", "aetos-greece", "bizerte-tunisia"], "landing_points_in_country": ["athens-greece", "chania-greece", "corfu-greece", "kokkini-greece", "aethos-greece", "syros-greece", "preveza-greece", "timpaki-greece", "tympaki-greece", "pachi-greece", "nopigeia-greece", "neapoli-greece", "korakia-greece", "porto-rafti-greece", "sitia-greece", "plimmiri-greece", "kremasti-greece", "kardamena-greece", "kochilari-greece", "perivolos-greece", "baxedes-greece", "pirgaki-greece", "naxos-greece", "filizi-greece", "naousa-greece", "kala<PERSON><PERSON>-greece", "mykonos-greece", "tinos-greece", "agios-sostis-greece", "ermoupoli-greece", "kavos-greece", "kontokali-greece", "plataria-greece", "aetos-greece"]}