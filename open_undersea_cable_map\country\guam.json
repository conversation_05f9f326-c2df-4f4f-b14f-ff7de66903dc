{"id": "guam", "name": "Guam", "cables": [{"id": "apricot", "name": "Apricot", "rfs_year": 2024, "is_planned": true}, {"id": "asia-america-gateway-aag-cable-system", "name": "Asia-America Gateway (AAG) Cable System", "rfs_year": 2009, "is_planned": false}, {"id": "asia-connect-cable-1-acc-1", "name": "Asia Connect Cable-1 (ACC-1)", "rfs_year": 2024, "is_planned": true}, {"id": "atisa", "name": "<PERSON><PERSON>", "rfs_year": 2017, "is_planned": false}, {"id": "australia-japan-cable-ajc", "name": "Australia-Japan Cable (AJC)", "rfs_year": 2001, "is_planned": false}, {"id": "bifrost", "name": "Bifrost", "rfs_year": 2024, "is_planned": true}, {"id": "echo", "name": "Echo", "rfs_year": 2023, "is_planned": true}, {"id": "guam-okinawa-kyushu-incheon-goki", "name": "Guam Okinawa Kyushu Incheon (GOKI)", "rfs_year": 2013, "is_planned": false}, {"id": "hantru1-cable-system", "name": "HANTRU1 Cable System", "rfs_year": 2010, "is_planned": false}, {"id": "japan-guam-australia-north-jga-n", "name": "Japan-Guam-Australia North (JGA-N)", "rfs_year": 2020, "is_planned": false}, {"id": "japan-guam-australia-south-jga-s", "name": "Japan-Guam-Australia South (JGA-S)", "rfs_year": 2020, "is_planned": false}, {"id": "mariana-guam-cable", "name": "Mariana-Guam Cable", "rfs_year": 1997, "is_planned": false}, {"id": "pipe-pacific-cable-1-ppc-1", "name": "PIPE Pacific Cable-1 (PPC-1)", "rfs_year": 2009, "is_planned": false}, {"id": "sea-us", "name": "SEA-US", "rfs_year": 2017, "is_planned": false}, {"id": "tata-tgn-pacific", "name": "Tata TGN-Pacific", "rfs_year": 2002, "is_planned": false}], "landing_points": ["paddington-nsw-australia", "oxford-falls-nsw-australia", "tanguisson-point-guam", "tumon-bay-guam", "maruyama-japan", "shima-japan", "los-angeles-ca-united-states", "toyohashi-japan", "hillsboro-or-united-states", "emi-japan", "piti-guam", "sydney-nsw-australia", "mersing-malaysia", "sriracha-thailand", "tungku-brunei", "vung-tau-vietnam", "lantau-island-china", "san-luis-obispo-ca-united-states", "keawaula-hi-united-states", "madang-papua-new-guinea", "majuro-marshall-islands", "kwajalein-marshall-islands", "pohnpei-micronesia", "la-union-philippines", "okinawa-japan", "kita-kyushu-japan", "saipan-northern-mariana-islands", "rota-northern-mariana-islands", "tinian-northern-mariana-islands", "changi-north-singapore", "davao-philippines", "hermosa-beach-ca-united-states", "makaha-hi-united-states", "kauditan-indonesia", "sugar-dock-saipan-northern-mariana-islands", "sasanlagu-rota-northern-mariana-islands", "tachognya-beach-tinian-northern-mariana-islands", "magachgil-yap-micronesia", "ngeremlengui-palau", "minamiboso-japan", "maroochydore-qld-australia", "eureka-ca-united-states", "tanjung-pakis-indonesia", "singapore-singapore", "agat-guam", "jakarta-indonesia", "tuas-singapore", "toucheng-taiwan", "brookvale-nsw-australia", "baler-philippines", "medan-indonesia", "batam-indonesia", "makassar-indonesia", "dili-timor-leste", "darwin-nt-australia", "manado-indonesia", "rosarito-mexico", "balikpapan-indonesia", "grover-beach-ca-united-states", "winema-road-beach-or-united-states"], "landing_points_in_country": ["tanguisson-point-guam", "tumon-bay-guam", "piti-guam", "agat-guam"]}