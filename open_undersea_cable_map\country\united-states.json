{"id": "united-states", "name": "United States", "cables": [{"id": "acs-alaska-oregon-network-akorn", "name": "ACS Alaska-Oregon Network (AKORN)", "rfs_year": 2009, "is_planned": false}, {"id": "aec-1", "name": "AEC-1", "rfs_year": 2016, "is_planned": false}, {"id": "alaska-united-east-au-east", "name": "Alaska United East (AU-East)", "rfs_year": 1999, "is_planned": false}, {"id": "alaska-united-southeast-au-se", "name": "Alaska United Southeast (AU-SE)", "rfs_year": 2008, "is_planned": false}, {"id": "alaska-united-turnagain-arm-auta", "name": "Alaska United Turnagain Arm (AUTA)", "rfs_year": 2012, "is_planned": false}, {"id": "alaska-united-west-au-west", "name": "Alaska United West (AU-West)", "rfs_year": 2004, "is_planned": false}, {"id": "america-movil-submarine-cable-system-1-amx-1", "name": "America Movil Submarine Cable System-1 (AMX-1)", "rfs_year": 2014, "is_planned": false}, {"id": "americas-i-north", "name": "Americas-I North", "rfs_year": 1994, "is_planned": false}, {"id": "americas-ii", "name": "Americas-II", "rfs_year": 2000, "is_planned": false}, {"id": "amitie", "name": "Amitie", "rfs_year": 2023, "is_planned": true}, {"id": "antillas-1", "name": "Antillas 1", "rfs_year": 1997, "is_planned": false}, {"id": "apollo", "name": "Apollo", "rfs_year": 2003, "is_planned": false}, {"id": "arcos", "name": "ARCOS", "rfs_year": 2001, "is_planned": false}, {"id": "asia-america-gateway-aag-cable-system", "name": "Asia-America Gateway (AAG) Cable System", "rfs_year": 2009, "is_planned": false}, {"id": "asia-connect-cable-1-acc-1", "name": "Asia Connect Cable-1 (ACC-1)", "rfs_year": 2024, "is_planned": true}, {"id": "atlantic-crossing-1-ac-1", "name": "Atlantic Crossing-1 (AC-1)", "rfs_year": 1998, "is_planned": false}, {"id": "<PERSON>-<PERSON><PERSON><PERSON>", "name": "AU-Aleutian", "rfs_year": 2022, "is_planned": true}, {"id": "bahamas-2", "name": "Bahamas 2", "rfs_year": 1997, "is_planned": false}, {"id": "bahamas-internet-cable-system-bics", "name": "Bahamas Internet Cable System (BICS)", "rfs_year": 2001, "is_planned": false}, {"id": "bifrost", "name": "Bifrost", "rfs_year": 2024, "is_planned": true}, {"id": "boriken-submarine-cable-system-bscs", "name": "Boriken Submarine Cable System (BSCS)", "rfs_year": 2024, "is_planned": true}, {"id": "brusa", "name": "BRUSA", "rfs_year": 2018, "is_planned": false}, {"id": "cap-1", "name": "CAP-1", "rfs_year": 2023, "is_planned": true}, {"id": "caribbean-express-cx", "name": "Caribbean Express (CX)", "rfs_year": 2025, "is_planned": true}, {"id": "carnival-submarine-network-1-csn-1", "name": "Carnival Submarine Network-1 (CSN-1)", "rfs_year": 2025, "is_planned": true}, {"id": "challenger-be<PERSON>uda-1-cb-1", "name": "Challenger Bermuda-1 (CB-1)", "rfs_year": 2008, "is_planned": false}, {"id": "colombia-florida-subsea-fiber-cfx-1", "name": "Colombia-Florida Subsea Fiber (CFX-1)", "rfs_year": 2008, "is_planned": false}, {"id": "columbus-ii-b", "name": "Columbus-II b", "rfs_year": 1994, "is_planned": false}, {"id": "confluence-1", "name": "Confluence-1", "rfs_year": 2023, "is_planned": true}, {"id": "crosslake-fibre", "name": "Crosslake Fibre", "rfs_year": 2019, "is_planned": false}, {"id": "curie", "name": "<PERSON><PERSON><PERSON>", "rfs_year": 2020, "is_planned": false}, {"id": "dunant", "name": "<PERSON><PERSON>", "rfs_year": 2021, "is_planned": false}, {"id": "echo", "name": "Echo", "rfs_year": 2023, "is_planned": true}, {"id": "exa-north-and-south", "name": "EXA North and South", "rfs_year": 2001, "is_planned": false}, {"id": "faster", "name": "FASTER", "rfs_year": 2016, "is_planned": false}, {"id": "firmina", "name": "Firmina", "rfs_year": 2023, "is_planned": true}, {"id": "flag-atlantic-1-fa-1", "name": "FLAG Atlantic-1 (FA-1)", "rfs_year": 2001, "is_planned": false}, {"id": "gemini-bermuda", "name": "Gemini Bermuda", "rfs_year": 2007, "is_planned": false}, {"id": "gignet-1", "name": "GigNet-1", "rfs_year": 2022, "is_planned": true}, {"id": "global-caribbean-network-gcn", "name": "Global Caribbean Network (GCN)", "rfs_year": 2006, "is_planned": false}, {"id": "globenet", "name": "GlobeNet", "rfs_year": 2000, "is_planned": false}, {"id": "grace-hopper", "name": "<PERSON>", "rfs_year": 2022, "is_planned": true}, {"id": "gtmo-1", "name": "GTMO-1", "rfs_year": 2016, "is_planned": false}, {"id": "gtmo-pr", "name": "GTMO-PR", "rfs_year": 2019, "is_planned": false}, {"id": "gulf-of-mexico-fiber-optic-network", "name": "Gulf of Mexico Fiber Optic Network", "rfs_year": 2008, "is_planned": false}, {"id": "havfrueaec-2", "name": "Havfrue/AEC-2", "rfs_year": 2020, "is_planned": false}, {"id": "hawaiki", "name": "Hawaiki", "rfs_year": 2018, "is_planned": false}, {"id": "hawaiki-nui", "name": "<PERSON><PERSON><PERSON>", "rfs_year": 2025, "is_planned": true}, {"id": "hics-hawaii-inter-island-cable-system", "name": "HICS (Hawaii Inter-Island Cable System)", "rfs_year": 1994, "is_planned": false}, {"id": "hifn-hawaii-island-fibre-network", "name": "HIFN (Hawaii Island Fibre Network)", "rfs_year": 1997, "is_planned": false}, {"id": "honotua", "name": "Honotua", "rfs_year": 2010, "is_planned": false}, {"id": "japan-u-s-cable-network-jus", "name": "Japan-U.S. Cable Network (JUS)", "rfs_year": 2001, "is_planned": false}, {"id": "juno", "name": "JUNO", "rfs_year": 2024, "is_planned": true}, {"id": "jupiter", "name": "JUPITER", "rfs_year": 2020, "is_planned": false}, {"id": "ketchcan1-submarine-fiber-cable-system", "name": "KetchCan1 Submarine Fiber Cable System", "rfs_year": 2020, "is_planned": false}, {"id": "kodiak-kenai-fiber-link-kkfl", "name": "Kodiak Kenai Fiber Link (KKFL)", "rfs_year": 2007, "is_planned": false}, {"id": "lynn-canal-fiber", "name": "Lynn Canal Fiber", "rfs_year": 2016, "is_planned": false}, {"id": "marea", "name": "MAREA", "rfs_year": 2018, "is_planned": false}, {"id": "maya-1", "name": "Maya-1", "rfs_year": 2000, "is_planned": false}, {"id": "mid-atlantic-crossing-mac", "name": "Mid-Atlantic Crossing (MAC)", "rfs_year": 2000, "is_planned": false}, {"id": "monet", "name": "Monet", "rfs_year": 2017, "is_planned": false}, {"id": "new-cross-pacific-ncp-cable-system", "name": "New Cross Pacific (NCP) Cable System", "rfs_year": 2018, "is_planned": false}, {"id": "northstar", "name": "NorthStar", "rfs_year": 1999, "is_planned": false}, {"id": "pacific-caribbean-cable-system-pccs", "name": "Pacific Caribbean Cable System (PCCS)", "rfs_year": 2015, "is_planned": false}, {"id": "pacific-crossing-1-pc-1", "name": "Pacific Crossing-1 (PC-1)", "rfs_year": 1999, "is_planned": false}, {"id": "pacific-light-cable-network-plcn", "name": "Pacific Light Cable Network (PLCN)", "rfs_year": 2022, "is_planned": false}, {"id": "pan-american-crossing-pac", "name": "Pan-American Crossing (PAC)", "rfs_year": 2000, "is_planned": false}, {"id": "paniolo-cable-network", "name": "Paniolo Cable Network", "rfs_year": 2009, "is_planned": false}, {"id": "quintillion-subsea-cable-network", "name": "Quintillion Subsea Cable Network", "rfs_year": 2017, "is_planned": false}, {"id": "sea-us", "name": "SEA-US", "rfs_year": 2017, "is_planned": false}, {"id": "sea2shore", "name": "sea2shore", "rfs_year": 2016, "is_planned": false}, {"id": "seabras-1", "name": "Seabras-1", "rfs_year": 2017, "is_planned": false}, {"id": "sealink", "name": "SEALink", "rfs_year": 2023, "is_planned": true}, {"id": "sint-maarten-puerto-rico-network-one-smpr-1", "name": "Sint Maarten Puerto Rico Network One (SMPR-1)", "rfs_year": 2004, "is_planned": false}, {"id": "south-america-1-sam-1", "name": "South America-1 (SAm-1)", "rfs_year": 2001, "is_planned": false}, {"id": "southern-caribbean-fiber", "name": "Southern Caribbean Fiber", "rfs_year": 2006, "is_planned": false}, {"id": "southern-cross-cable-network-sccn", "name": "Southern Cross Cable Network (SCCN)", "rfs_year": 2000, "is_planned": false}, {"id": "southern-cross-next", "name": "Southern Cross NEXT", "rfs_year": 2022, "is_planned": false}, {"id": "st-thomas-st-croix-system", "name": "St<PERSON> Thomas-St. Croix System", "rfs_year": 1997, "is_planned": false}, {"id": "taino-carib", "name": "Taino-Carib", "rfs_year": 1992, "is_planned": false}, {"id": "tata-tgn-atlantic", "name": "Tata TGN-Atlantic", "rfs_year": 2001, "is_planned": false}, {"id": "tata-tgn-pacific", "name": "Tata TGN-Pacific", "rfs_year": 2002, "is_planned": false}, {"id": "telstra-endeavour", "name": "Telstra Endeavour", "rfs_year": 2008, "is_planned": false}, {"id": "terra-sw", "name": "TERRA SW", "rfs_year": 2012, "is_planned": false}, {"id": "trans-pacific-express-tpe-cable-system", "name": "Trans-Pacific Express (TPE) Cable System", "rfs_year": 2008, "is_planned": false}, {"id": "unityeac-pacific", "name": "Unity/EAC-Pacific", "rfs_year": 2010, "is_planned": false}, {"id": "wall-li", "name": "WALL-LI", "rfs_year": 2024, "is_planned": true}, {"id": "yellow", "name": "Yellow", "rfs_year": 2000, "is_planned": false}], "landing_points": ["grover-beach-ca-united-states", "shima-japan", "ajigaura-japan", "brookhaven-ny-united-states", "beverwijk-netherlands", "whitesands-bay-united-kingdom", "st-croix-virgin-islands-u-s-", "hollywood-fl-united-states", "tijuana-mexico", "northport-ny-united-states", "kitaibaraki-japan", "maruyama-japan", "makaha-hi-united-states", "morro-bay-ca-united-states", "manchester-ca-united-states", "island-park-ny-united-states", "tuckerton-nj-united-states", "st-davids-bermuda", "boca-raton-fl-united-states", "sylt-germany", "nassau-bahamas", "crooked-island-bahamas", "providenciales-turks-and-caicos-islands", "punta-cana-dominican-republic", "puerto-plata-dominican-republic", "riohacha-colombia", "bluefields-nicaragua", "puerto-lempira-honduras", "trujillo-honduras", "puerto-cortes-honduras", "puerto-barrios-guatemala", "halifax-ns-canada", "lynn-ma-united-states", "dublin-ireland", "southport-united-kingdom", "bude-united-kingdom", "san-juan-pr-united-states", "fortaleza-brazil", "rio-de-janeiro-brazil", "santos-brazil", "las-toninas-argentina", "valparaso-chile", "arica-chile", "lurin-peru", "puerto-san-jose-guatemala", "puerto-limon-costa-rica", "vero-beach-fl-united-states", "eight-mile-rock-bahamas", "mazatln-mexico", "fort-amador-panama", "puerto-cabezas-nicaragua", "cayenne-french-guiana", "le-lamentin-martinique", "miramar-pr-united-states", "port-of-spain-trinidad-and-tobago", "camuri-venezuela", "cartagena-colombia", "cat-island-bahamas", "salvador-brazil", "wall-township-nj-united-states", "whittier-ak-united-states", "valdez-ak-united-states", "juneau-ak-united-states", "bellport-ny-united-states", "maria-chiquita-panama", "tolu-colombia", "half-moon-bay-cayman-islands", "skewjack-united-kingdom", "hillsboro-or-united-states", "lena-point-ak-united-states", "punto-fijo-venezuela", "lynnwood-wa-united-states", "los-angeles-ca-united-states", "toyohashi-japan", "brookvale-nsw-australia", "emi-japan", "north-miami-beach-fl-united-states", "caves-point-bahamas", "current-bah<PERSON>s", "sandy-point-bahamas", "highbridge-united-kingdom", "tulum-mexico", "isla-verde-pr-united-states", "piti-guam", "hawksbill-bahamas", "cancn-mexico", "belize-city-belize", "plerin-france", "harbour-pointe-wa-united-states", "santo-domingo-dominican-republic", "magens-bay-vi-united-states", "condado-beach-pr-united-states", "seward-ak-united-states", "warrenton-or-united-states", "lihue-hi-united-states", "kihei-maui-hi-united-states", "kawaihae-hi-united-states", "koko-head-hi-united-states", "ko-olina-hi-united-states", "manele-bay-hi-united-states", "kaunakakai-hi-united-states", "unqui-costa-rica", "qingdao-china", "chongming-china", "tanshui-taiwan", "nedonna-beach-or-united-states", "spanish-river-park-fl-united-states", "crown-haven-bahamas", "riding-point-bahamas", "anchorage-ak-united-states", "homer-ak-united-states", "kenai-ak-united-states", "kodiak-ak-united-states", "narrow-cape-ak-united-states", "barranquilla-colombia", "punta-carnero-ecuador", "paddington-nsw-australia", "keawaula-hi-united-states", "mancora-peru", "manasquan-nj-united-states", "mersing-malaysia", "sriracha-thailand", "tungku-brunei", "vung-tau-vietnam", "lantau-island-china", "san-luis-obispo-ca-united-states", "morant-point-jamaica", "florence-or-united-states", "nikiski-ak-united-states", "tanguisson-point-guam", "wrangell-ak-united-states", "petersburg-ak-united-states", "hawk-inlet-ak-united-states", "angoon-ak-united-states", "sitka-ak-united-states", "chikura-japan", "papenoo-french-polynesia", "paget-bermuda", "baillif-guadeloupe", "needhams-point-barbados", "basseterre-saint-kitts-and-nevis", "rodney-bay-saint-lucia", "kingstown-saint-vincent-and-the-grenadines", "point-salines-grenada", "chaguaramas-trinidad-and-tobago", "canefield-dominica", "coleraine-united-kingdom", "west-palm-beach-fl-united-states", "redondo-beach-ca-united-states", "ketchikan-ak-united-states", "la-union-philippines", "jacksonville-fl-united-states", "shirley-ny-united-states", "willemstad-curaao", "manta-ecuador", "balboa-panama", "tortola-virgin-islands-u-k-", "hudishibana-aruba", "sydney-nsw-australia", "christiansted-vi-united-states", "frederiksted-virgin-islands-u-s-", "brewers-bay-virgin-islands-u-s-", "great-bay-virgin-islands-u-s-", "flamingo-bay-virgin-islands-u-s-", "banana-bay-virgin-islands-u-s-", "vila-olga-virgin-islands-u-s-", "busan-south-korea", "mangawhai-new-zealand", "changi-north-singapore", "killala-ireland", "vaitape-french-polynesia", "uturoa-french-polynesia", "moorea-french-polynesia", "huahine-french-polynesia", "portage-ak-united-states", "mchugh-point-ak-united-states", "williamsport-ak-united-states", "pedro-bay-ak-united-states", "pile-bay-ak-united-states", "illiamna-ak-united-states", "kokhanok-ak-united-states", "newhalen-ak-united-states", "nondalton-ak-united-states", "fish-camp-ak-united-states", "port-alsworth-ak-united-states", "igiugig-ak-united-states", "bandon-or-united-states", "davao-philippines", "hermosa-beach-ca-united-states", "kapolei-hi-united-states", "nanhui-china", "toucheng-taiwan", "pacific-city-or-united-states", "jarry-guadeloupe", "saint-martin-saint-martin", "saint-<PERSON><PERSON><PERSON><PERSON>-saint-bar<PERSON><PERSON><PERSON>", "haines-ak-united-states", "skagway-ak-united-states", "alexandria-nsw-australia", "kahe-point-hi-united-states", "spencer-beach-hi-united-states", "suva-fiji", "takapuna-new-zealand", "whenuapai-new-zealand", "lannion-france", "prudhoe-bay-ak-united-states", "nome-ak-united-states", "kotzebue-ak-united-states", "wainwright-ak-united-states", "point-hope-ak-united-states", "utqiavik-ak-united-states", "praia-grande-brazil", "kauditan-indonesia", "virginia-beach-va-united-states", "pago-pago-american-samoa", "bilbao-spain", "philipsburg-sint-maarten", "baie-longue-saint-martin", "el-segundo-ca-united-states", "magachgil-yap-micronesia", "ngeremlengui-palau", "copa-club-jamaica", "baie-mahault-guadeloupe", "st-louis-saint-martin", "gustavia-saint-<PERSON><PERSON><PERSON><PERSON>", "guantanamo-bay-cuba", "dania-beach-fl-united-states", "punta-salina-pr-united-states", "savusavu-fiji", "nukunonu-tokelau", "kiritimati-kiribati", "toronto-on-canada", "buffalo-ny-united-states", "westbury-ny-united-states", "daet-philippines", "lecanvey-ireland", "false-pass-ak-united-states", "akutan-ak-united-states", "makena-hi-united-states", "lahaina-hi-united-states", "kekaha-hi-united-states", "hawaii-kai-hi-united-states", "blaabjerg-denmark", "kristiansand-norway", "narragansett-ri-united-states", "crescent-beach-ri-united-states", "mahuma-curaao", "king-cove-ak-united-states", "cold-bay-ak-united-states", "sand-point-ak-united-states", "perryville-ak-united-states", "chignik-bay-ak-united-states", "chignik-lagoon-ak-united-states", "chignik-lake-ak-united-states", "larsen-bay-ak-united-states", "unalaska-ak-united-states", "saint-hilair<PERSON>-<PERSON>-riez-france", "cloverdale-or-united-states", "charlestown-ri-united-states", "maiquetia-venezuela", "prince-rupert-bc-canada", "macao-beach-dominican-republic", "aguadilla-pr-united-states", "ponce-pr-united-states", "humacao-pr-united-states", "myrtle-beach-sc-united-states", "sunny-isles-fl-united-states", "geoje-south-korea", "baler-philippines", "eureka-ca-united-states", "tanjung-pakis-indonesia", "bunkum-bay-montserrat", "dickenson-bay-antigua-and-barbuda", "freeport-tx-united-states", "pascagoula-ms-united-states", "le-porge-france", "lingang-china", "pagudpud-philippines", "coffman-cove-ak-united-states", "singapore-singapore", "agat-guam", "jakarta-indonesia", "punta-del-este-uruguay", "batam-indonesia", "darwin-nt-australia", "brisbane-qld-australia", "melbourne-vic-australia", "invercargill-new-zealand", "dunedin-new-zealand", "christchurch-new-zealand", "schooner-bight-colombia", "medan-indonesia", "makassar-indonesia", "dili-timor-leste", "manado-indonesia", "ancon-ecuador", "panama-city-panama", "cristbal-ecuador", "naples-fl-united-states", "rosarito-mexico", "balikpapan-indonesia", "winema-road-beach-or-united-states"], "landing_points_in_country": ["bandon-or-united-states", "boca-raton-fl-united-states", "buffalo-ny-united-states", "charlestown-ri-united-states", "harbour-pointe-wa-united-states", "hillsboro-or-united-states", "hollywood-fl-united-states", "jacksonville-fl-united-states", "los-angeles-ca-united-states", "lynn-ma-united-states", "manasquan-nj-united-states", "manchester-ca-united-states", "myrtle-beach-sc-united-states", "naples-fl-united-states", "nedonna-beach-or-united-states", "pacific-city-or-united-states", "san-luis-obispo-ca-united-states", "shirley-ny-united-states", "tuckerton-nj-united-states", "vero-beach-fl-united-states", "wall-township-nj-united-states", "west-palm-beach-fl-united-states", "anchorage-ak-united-states", "el-segundo-ca-united-states", "san-juan-pr-united-states", "brookhaven-ny-united-states", "juneau-ak-united-states", "lihue-hi-united-states", "virginia-beach-va-united-states", "hermosa-beach-ca-united-states", "redondo-beach-ca-united-states", "grover-beach-ca-united-states", "morro-bay-ca-united-states", "spencer-beach-hi-united-states", "kahe-point-hi-united-states", "keawaula-hi-united-states", "seward-ak-united-states", "magens-bay-vi-united-states", "makaha-hi-united-states", "miramar-pr-united-states", "valdez-ak-united-states", "lena-point-ak-united-states", "bellport-ny-united-states", "lynnwood-wa-united-states", "north-miami-beach-fl-united-states", "isla-verde-pr-united-states", "condado-beach-pr-united-states", "warrenton-or-united-states", "whittier-ak-united-states", "freeport-tx-united-states", "westbury-ny-united-states", "eureka-ca-united-states", "kihei-maui-hi-united-states", "spanish-river-park-fl-united-states", "homer-ak-united-states", "kenai-ak-united-states", "kodiak-ak-united-states", "narrow-cape-ak-united-states", "florence-or-united-states", "nikiski-ak-united-states", "wrangell-ak-united-states", "petersburg-ak-united-states", "ketchikan-ak-united-states", "hawk-inlet-ak-united-states", "angoon-ak-united-states", "sitka-ak-united-states", "kawaihae-hi-united-states", "northport-ny-united-states", "island-park-ny-united-states", "prudhoe-bay-ak-united-states", "christiansted-vi-united-states", "portage-ak-united-states", "mchugh-point-ak-united-states", "williamsport-ak-united-states", "pedro-bay-ak-united-states", "pile-bay-ak-united-states", "illiamna-ak-united-states", "kokhanok-ak-united-states", "newhalen-ak-united-states", "nondalton-ak-united-states", "fish-camp-ak-united-states", "port-alsworth-ak-united-states", "igiugig-ak-united-states", "koko-head-hi-united-states", "ko-olina-hi-united-states", "kaunakakai-hi-united-states", "manele-bay-hi-united-states", "haines-ak-united-states", "skagway-ak-united-states", "kapolei-hi-united-states", "nome-ak-united-states", "kotzebue-ak-united-states", "wainwright-ak-united-states", "point-hope-ak-united-states", "utqiavik-ak-united-states", "dania-beach-fl-united-states", "punta-salina-pr-united-states", "false-pass-ak-united-states", "akutan-ak-united-states", "unalaska-ak-united-states", "makena-hi-united-states", "lahaina-hi-united-states", "kekaha-hi-united-states", "hawaii-kai-hi-united-states", "pascagoula-ms-united-states", "narragansett-ri-united-states", "crescent-beach-ri-united-states", "king-cove-ak-united-states", "cold-bay-ak-united-states", "sand-point-ak-united-states", "perryville-ak-united-states", "chignik-bay-ak-united-states", "chignik-lagoon-ak-united-states", "chignik-lake-ak-united-states", "larsen-bay-ak-united-states", "cloverdale-or-united-states", "aguadilla-pr-united-states", "ponce-pr-united-states", "humacao-pr-united-states", "sunny-isles-fl-united-states", "coffman-cove-ak-united-states", "winema-road-beach-or-united-states"]}