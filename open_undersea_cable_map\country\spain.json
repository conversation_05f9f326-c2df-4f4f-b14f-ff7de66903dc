{"id": "spain", "name": "Spain", "cables": [{"id": "2africa", "name": "2Africa", "rfs_year": 2023, "is_planned": true}, {"id": "africa-coast-to-europe-ace", "name": "Africa Coast to Europe (ACE)", "rfs_year": 2012, "is_planned": false}, {"id": "almera-melilla-alme", "name": "Almería-Melilla (ALME)", "rfs_year": 1990, "is_planned": false}, {"id": "alpal-2", "name": "ALPAL-2", "rfs_year": 2002, "is_planned": false}, {"id": "balalink", "name": "Balalink", "rfs_year": 2001, "is_planned": false}, {"id": "canalink", "name": "Canalink", "rfs_year": 2011, "is_planned": false}, {"id": "candalta", "name": "CANDALTA", "rfs_year": 1999, "is_planned": false}, {"id": "dos-continentes-l-ll", "name": "DOS CONTINENTES l & ll", "rfs_year": 2020, "is_planned": false}, {"id": "estepona-tetouan", "name": "Estepona-Tetouan", "rfs_year": 1994, "is_planned": false}, {"id": "flag-europe-asia-fea", "name": "FLAG Europe-Asia (FEA)", "rfs_year": 1997, "is_planned": false}, {"id": "grace-hopper", "name": "<PERSON>", "rfs_year": 2022, "is_planned": true}, {"id": "la-gomera-el-hierro", "name": "La Gomera-El Hierro", "rfs_year": null, "is_planned": false}, {"id": "marea", "name": "MAREA", "rfs_year": 2018, "is_planned": false}, {"id": "medusa-submarine-cable-system", "name": "Medusa Submarine Cable System", "rfs_year": 2024, "is_planned": true}, {"id": "oran-valencia-orval", "name": "Oran-Valencia (ORVAL)", "rfs_year": 2020, "is_planned": false}, {"id": "penbal-4", "name": "Penbal-4", "rfs_year": 1991, "is_planned": false}, {"id": "penbal-5", "name": "Penbal-5", "rfs_year": 1994, "is_planned": false}, {"id": "pencan-8", "name": "Pencan-8", "rfs_year": 2011, "is_planned": false}, {"id": "pencan-9", "name": "Pencan-9", "rfs_year": null, "is_planned": false}, {"id": "romulo", "name": "<PERSON><PERSON><PERSON>", "rfs_year": 2011, "is_planned": false}, {"id": "roquetas-melilla-cam", "name": "Roquetas-<PERSON><PERSON> (CAM)", "rfs_year": 2014, "is_planned": false}, {"id": "sat-3wasc", "name": "SAT-3/WASC", "rfs_year": 2002, "is_planned": false}, {"id": "subcan-link-1", "name": "Subcan Link 1", "rfs_year": 2002, "is_planned": false}, {"id": "subcan-link-2", "name": "Subcan Link 2", "rfs_year": 2002, "is_planned": false}, {"id": "tata-tgn-western-europe", "name": "Tata TGN-Western Europe", "rfs_year": 2002, "is_planned": false}, {"id": "tegopa", "name": "TEGOPA", "rfs_year": 1994, "is_planned": false}, {"id": "tenerife-gran-canaria", "name": "Tenerife-Gran Canaria", "rfs_year": 1999, "is_planned": false}, {"id": "tenerife-la-palma", "name": "Tenerife-La Palma", "rfs_year": null, "is_planned": false}, {"id": "transcan-2", "name": "TRANSCAN-2", "rfs_year": 1990, "is_planned": false}, {"id": "transcan-3", "name": "TRANSCAN-3", "rfs_year": 2000, "is_planned": false}, {"id": "west-africa-cable-system-wacs", "name": "West Africa Cable System (WACS)", "rfs_year": 2012, "is_planned": false}], "landing_points": ["porthcurno-united-kingdom", "mumbai-india", "estepona-spain", "palermo-italy", "alexandria-egypt", "suez-egypt", "fujairah-united-arab-emirates", "penang-malaysia", "songkhla-thailand", "lantau-island-china", "miura-japan", "jeddah-saudi-arabia", "aqaba-jordan", "sesimbra-portugal", "alta-vista-canary-islands-spain", "dakar-senegal", "accra-ghana", "cotonou-benin", "lagos-nigeria", "libreville-gabon", "cacuaco-angola", "melkbosstrand-south-africa", "douala-cameroon", "abidjan-cte-divoire", "seixal-portugal", "highbridge-united-kingdom", "bilbao-spain", "satun-thailand", "el-djamila-algeria", "ses-covetes-spain", "ttouan-morocco", "almera-spain", "melilla-spain", "valencia-spain", "mallorca-spain", "ibiza-spain", "gav-spain", "candelaria-canary-islands-spain", "penmarch-france", "nouakchott-mauritania", "banjul-gambia", "conakry-guinea", "freetown-sierra-leone", "monrovia-liberia", "muanda-congo-dem-rep-", "pointe-noire-congo-rep-", "lome-togo", "bata-equatorial-guinea", "yzerfontein-south-africa", "swakopmund-namibia", "limbe-cameroon", "oran-algeria", "kribi-cameroon", "rota-spain", "asilah-morocco", "santa-cruz-de-la-palma-canary-islands-spain", "palma-spain", "conil-spain", "el-goro-canary-islands-spain", "sangano-angola", "las-caletillas-spain", "piedra-santa-spain", "tinocas-canary-islands-spain", "gimar-canary-islands-spain", "chipiona-spain", "tarahales-spain", "duynefontein-south-africa", "virginia-beach-va-united-states", "carcavelos-portugal", "suro-guinea-bissau", "praia-cape-verde", "algiers-algeria", "playa-blanca-canary-islands-spain", "arrecife-canary-islands-spain", "puerto-del-rosario-canary-islands-spain", "morrojable-canary-islands-spain", "aguimes-canary-islands-spain", "el-mdano-canary-islands-spain", "sardina-canary-islands-spain", "granadilla-canary-islands-spain", "los-realejos-canary-islands-spain", "san-sebastian-de-la-gomera-canary-islands-spain", "valverde-canary-islands-spain", "geoje-south-korea", "playa-de-benitez-spain", "tarifa-spain", "playa-de-la-ribera-spain", "la-lnea-spain", "ras-ghareb-egypt", "port-said-egypt", "bude-united-kingdom", "genoa-italy", "marseille-france", "barcelona-spain", "port-sudan-sudan", "salal<PERSON>-<PERSON>man", "djibouti-city-djibouti", "mogadishu-somalia", "mombasa-kenya", "dar-es-salaam-tanzania", "maputo-mozambique", "nacala-mozambique", "mahajanga-madagascar", "mtunzini-south-africa", "cape-town-south-africa", "port-elizabeth-south-africa", "yanbu-saudi-arabia", "zafarana-egypt", "sao-tome-sao-tome-and-principe", "bellport-ny-united-states", "nanhui-china", "kwa-ibo-nigeria", "luanda-angola", "moroni-comoros", "gran-canaria-canary-islands-spain", "carana-seychelles", "barka-oman", "abu-dhabi-united-arab-emirates", "kalba-united-arab-emirates", "doha-qatar", "manama-bahrain", "al-faw-iraq", "kuwait-city-kuwait", "al-khobar-saudi-arabia", "karachi-pakistan", "duba-saudi-arabia", "mazara-del-vallo-italy", "zahara-de-los-atunes-spain", "torreguadiaro-spain", "nador-morocco", "collo-algeria", "tympaki-greece", "sines-portugal", "sagunto-spain", "santa-ponsa-spain", "berbera-somalia", "bizerte-tunisia", "roquetas-de-mar-spain"], "landing_points_in_country": ["barcelona-spain", "valencia-spain", "bilbao-spain", "mallorca-spain", "almera-spain", "palma-spain", "alta-vista-canary-islands-spain", "gran-canaria-canary-islands-spain", "el-mdano-canary-islands-spain", "estepona-spain", "conil-spain", "ses-covetes-spain", "melilla-spain", "ibiza-spain", "roquetas-de-mar-spain", "gav-spain", "candelaria-canary-islands-spain", "santa-cruz-de-la-palma-canary-islands-spain", "chipiona-spain", "sardina-canary-islands-spain", "rota-spain", "el-goro-canary-islands-spain", "las-caletillas-spain", "piedra-santa-spain", "tinocas-canary-islands-spain", "gimar-canary-islands-spain", "tarahales-spain", "playa-blanca-canary-islands-spain", "arrecife-canary-islands-spain", "puerto-del-rosario-canary-islands-spain", "morrojable-canary-islands-spain", "aguimes-canary-islands-spain", "granadilla-canary-islands-spain", "los-realejos-canary-islands-spain", "san-sebastian-de-la-gomera-canary-islands-spain", "valverde-canary-islands-spain", "tarifa-spain", "playa-de-benitez-spain", "playa-de-la-ribera-spain", "la-lnea-spain", "zahara-de-los-atunes-spain", "torreguadiaro-spain", "sagunto-spain", "santa-ponsa-spain"]}